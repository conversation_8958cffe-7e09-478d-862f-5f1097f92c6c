'use client';

import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { FiArrowRight } from 'react-icons/fi';
import { Card } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { useTheme } from '@/hooks/useTheme';
import * as LucideIcons from 'lucide-react';
import { useState, useCallback } from 'react';
import { useLoading } from '@/contexts/LoadingContext';
import { Progress } from '@/components/ui/nprogress';

interface UnifiedCardProps {
  // Core data
  id: string;
  title: string;
  description: string;
  icon: string;

  // Type and routing - updated to support unified routing
  type?: 'tool' | 'calculator' | 'blog';
  category?: string;

  // Optional metadata
  inputFormat?: string;
  outputFormat?: string;
  popular?: boolean;
  comingSoon?: boolean;

  // Blog-specific
  author?: string;
  date?: string;
  image?: string;

  // Animation
  index?: number;
  delay?: number;

  // Styling
  className?: string;
  variant?: 'default' | 'enhanced' | 'minimal';
}

export default function UnifiedCard({
  id,
  title,
  description,
  icon,
  type = 'tool',
  category,
  inputFormat,
  outputFormat,
  popular = false,
  comingSoon = false,
  author,
  date,
  image,
  index = 0,
  delay,
  className,
  variant = 'enhanced'
}: UnifiedCardProps) {
  const router = useRouter();
  const { theme } = useTheme();
  const { startLoading } = useLoading();
  const [isNavigating, setIsNavigating] = useState(false);

  const isDark = theme === 'dark';

  // Calculate animation delay
  const animationDelay = delay ?? 0.05 * (index % 8);

  // Generate the correct path using unified routing
  const getUnifiedHref = () => {
    switch (type) {
      case 'calculator':
        return `/calculators/${id}`;
      case 'tool':
        return `/tools/${id}`;
      case 'blog':
        return `/blogs/${id}`;
      default:
        return `/tools/${id}`;
    }
  };

  const href = getUnifiedHref();

  // Handle click with improved navigation and loading indicators
  const handleClick = useCallback((e: React.MouseEvent) => {
    if (comingSoon) {
      e.preventDefault();
      return;
    }

    // Start loading indicators
    setIsNavigating(true);
    startLoading();
    Progress.start();

    // Reset loading state after navigation attempt
    const timeoutId = setTimeout(() => {
      setIsNavigating(false);
    }, 2000);

    // Clean up timeout if component unmounts
    return () => clearTimeout(timeoutId);
  }, [comingSoon, startLoading]);

  // Icon rendering for all content types
  const renderIcon = (iconName: string, className: string = "w-8 h-8") => {
    if (type === 'tool') {
      return <span className="text-4xl">{iconName}</span>;
    }

    if (type === 'blog') {
      // For blogs, show image if available, otherwise use a default icon
      if (image) {
        return (
          <div className="w-12 h-12 rounded-lg overflow-hidden">
            <img
              src={image}
              alt={title}
              className="w-full h-full object-cover"
            />
          </div>
        );
      }
      return <LucideIcons.FileText className={className} />;
    }

    // For calculators, use Lucide icons
    const pascalCaseName = iconName
      .split("-")
      .map(part => part.charAt(0).toUpperCase() + part.slice(1))
      .join("");

    const IconComponent = (LucideIcons as any)[pascalCaseName];

    if (IconComponent) {
      return <IconComponent className={className} />;
    }

    return <LucideIcons.Calculator className={className} />;
  };

  // Category colors for all content types
  const getCategoryColor = () => {
    if (type === 'calculator') {
      const categoryColors = {
        finance: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
        math: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
        conversion: 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300',
        health: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300',
      };
      return categoryColors[category as keyof typeof categoryColors] || 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }

    if (type === 'blog') {
      const blogColors = {
        'Tips & Tricks': 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900/30 dark:text-emerald-300',
        'Conversion': 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
        'Security': 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300',
        'Accessibility': 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300',
        'Comparison': 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300',
        'Optimization': 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300',
      };
      return blogColors[category as keyof typeof blogColors] || 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }

    // Tool colors
    const toolColors = {
      pdf: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
      office: 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300',
      image: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
      web: 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300',
    };
    return toolColors[category as keyof typeof toolColors] || 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
  };

  // Card content
  const cardContent = (
    <div className="flex flex-col h-full">
      {/* Header with icon and category */}
      <div className="flex items-start justify-between mb-4">
        <motion.div
          className="relative"
          whileHover={{
            scale: type === 'calculator' ? 1.2 : type === 'blog' ? 1.05 : 1.1,
            rotate: type === 'calculator' ? 10 : type === 'blog' ? 2 : 5
          }}
          transition={{ duration: 0.3, ease: "easeOut" }}
        >
          {renderIcon(icon)}

          {/* Enhanced glow effect for all types */}
          <div className={`absolute inset-0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-md -z-10 ${
            type === 'calculator'
              ? 'bg-yellow-400/30'
              : type === 'blog'
              ? 'bg-green-400/30'
              : 'bg-blue-400/20'
          }`} />
        </motion.div>

        {category && (
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCategoryColor()}`}>
            {category}
          </span>
        )}

        {popular && (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300">
            Popular
          </span>
        )}
      </div>

      {/* Title and description */}
      <h3 className="text-xl font-semibold mb-3 group-hover:text-blue-500 transition-colors flex-shrink-0"
        style={{ color: 'var(--text-primary)' }}
      >
        {title}
      </h3>

      <p className="mb-4 flex-grow" style={{ color: 'var(--text-secondary)' }}>
        {description}
      </p>

      {/* Footer */}
      <div className="flex justify-between items-center mt-auto">
        {/* Tool format info */}
        {type === 'tool' && inputFormat && outputFormat && (
          <div className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
            {inputFormat} → {outputFormat}
          </div>
        )}

        {/* Blog metadata */}
        {type === 'blog' && (author || date) && (
          <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
            {author && (
              <span className="flex items-center gap-1">
                <LucideIcons.User className="w-3 h-3" />
                {author}
              </span>
            )}
            {date && (
              <span className="flex items-center gap-1">
                <LucideIcons.Calendar className="w-3 h-3" />
                {date}
              </span>
            )}
          </div>
        )}

        <motion.div
          whileHover={{ x: 4, scale: 1.1 }}
          transition={{ duration: 0.2 }}
          className="ml-auto"
        >
          <FiArrowRight className={`w-5 h-5 transition-all duration-300 ${
            type === 'calculator'
              ? 'text-yellow-500 group-hover:text-yellow-600 dark:text-yellow-400 dark:group-hover:text-yellow-300'
              : type === 'blog'
              ? 'text-green-500 group-hover:text-green-600 dark:text-green-400 dark:group-hover:text-green-300'
              : 'text-gray-400 group-hover:text-blue-500 dark:group-hover:text-blue-400'
          }`} />
        </motion.div>
      </div>
    </div>
  );

  // Coming soon overlay
  const comingSoonOverlay = comingSoon && (
    <div className="absolute inset-0 flex items-center justify-center backdrop-blur-[2px] z-10 rounded-xl"
      style={{
        backgroundColor: isDark ? 'rgba(17, 24, 39, 0.8)' : 'rgba(255, 255, 255, 0.8)'
      }}
    >
      <motion.span
        whileHover={{ scale: 1.05 }}
        className="px-4 py-1.5 bg-blue-600 text-white rounded-full text-sm font-medium shadow-md"
      >
        Coming Soon
      </motion.span>
    </div>
  );

  return (
    <motion.div
      initial={{ opacity: 0, y: 20, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{ duration: 0.3, delay: animationDelay }}
      whileHover={{
        y: type === 'calculator' ? -8 : type === 'blog' ? -3 : -5,
        scale: type === 'calculator' ? 1.03 : type === 'blog' ? 1.01 : 1.02,
        transition: { duration: 0.2, ease: "easeOut" }
      }}
      className={cn("h-full", className)}
    >
      {comingSoon ? (
        <Card
          className={cn(
            "group relative h-full p-6 rounded-xl border transition-all duration-300 overflow-hidden shadow-sm hover:shadow-md cursor-not-allowed",
            variant === 'enhanced' && type === 'calculator' && !isDark && "bg-gradient-to-br from-yellow-50 to-white",
            variant === 'enhanced' && type === 'blog' && !isDark && "bg-gradient-to-br from-green-50 to-white",
            isNavigating && "opacity-75"
          )}
          style={{
            backgroundColor: isDark ? 'var(--bg-secondary)' : undefined,
            borderColor: isDark ? '#374151' : '#e5e7eb',
            color: 'var(--text-primary)'
          }}
        >
          {cardContent}
          {comingSoonOverlay}
        </Card>
      ) : (
        <Link href={href} onClick={handleClick} className="block h-full">
          <Card
            className={cn(
              "group relative h-full p-6 rounded-xl border transition-all duration-300 overflow-hidden shadow-sm hover:shadow-md cursor-pointer",
              variant === 'enhanced' && type === 'calculator' && !isDark && "bg-gradient-to-br from-yellow-50 to-white",
              isNavigating && "opacity-75"
            )}
            style={{
              backgroundColor: isDark ? 'var(--bg-secondary)' : undefined,
              borderColor: isDark ? '#374151' : '#e5e7eb',
              color: 'var(--text-primary)'
            }}
          >
            {cardContent}

            {/* Gradient background effect on hover */}
            <div className="absolute inset-0 -z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
              style={{
                background: isDark
                  ? 'linear-gradient(to bottom right, rgba(30, 64, 175, 0.2), rgba(31, 41, 55, 0.8))'
                  : 'linear-gradient(to bottom right, rgba(219, 234, 254, 0.8), rgba(255, 255, 255, 0.8))'
              }}
            />

            {/* Subtle border glow effect on hover */}
            <div className="absolute inset-0 -z-20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-xl rounded-xl"
              style={{
                backgroundColor: isDark ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.05)'
              }}
            />
          </Card>
        </Link>
      )}
    </motion.div>
  );
}
